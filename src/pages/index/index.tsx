import { SafeArea } from '@nutui/nutui-react-taro';
import { Image, Navigator, Text, View } from '@tarojs/components';
import Taro, { useShareAppMessage, useShareTimeline } from '@tarojs/taro';
import { useEffect, useState } from 'react';
import local from '../../assets/local.svg';
import title from '../../assets/title.svg';
import Background from '../../components/background';
import NavBar from '../../components/nav-bar';
import RecommendList from '../../components/recommend-list';
import SearchBar from '../../components/search-bar';
import useRequest from '../../hooks/use-request';
import getImageUrl from '../../utils/get-image-url';
import TabBar from './tab-bar';

export default function Index() {
    const [address, setAddress] = useState<string>();
    const [showRegistrationMenu, setShowRegistrationMenu] = useState(false);

    // 配置页面转发
    useShareAppMessage(() => {
        return {
            title: '塞上早茶 - 吴忠美食景点攻略',
            path: '/pages/index/index',
            imageUrl: require('../../assets/home.png')
        };
    });

    // 配置分享到朋友圈
    useShareTimeline(() => {
        return {
            title: '塞上早茶 - 吴忠美食景点攻略',
            imageUrl: require('../../assets/home.png')
        };
    });

    useEffect(() => {
        Taro.eventCenter.on('location', function({ address }) {
            setAddress(address);
        });

        return () => {
            Taro.eventCenter.off('location');
        };
    }, []);

    const { result: coupon = {} } = useRequest({
        url: 'setting/coupon'
    });

    return <View>
        <Background className={'rounded-b-[12px]'}>
            <NavBar>
                <View className={'flex items-center justify-between'}>
                    <Image className={'h-[25px] w-[80px]'} src={title} />
                    {address && <View className={'text-[14px] flex items-center gap-[4px] ml-[20px]'}>
                        <Image className={'w-[16px] h-[16px]'} src={local} />{address}
                    </View>}
                </View>
            </NavBar>
            <SearchBar placeholder={'搜索商户、景点、酒店'} type='eatery' />
        </Background>
        <View className={'relative pt-[96px] px-[15px] mt-[5px] mb-[10px]'}>
            <View onClick={() => {
                Taro.navigateTo({
                    url: '/pages/chat/index'
                });
            }}>
                <Image className={'absolute top-0 left-[6px] right-[6px] w-auto z-[1]'} mode={'widthFix'} src={require('../../assets/welcome.svg')} />
                <Image onLongPress={() => {
                    setShowRegistrationMenu(true);
                }} className={'w-[95px] h-[86px] absolute z-[2] top-[4px] left-[24px]'} src={require('../../assets/logo.png')} />
                <View className={'absolute z-[2] top-[27px] right-[35px] text-[18px] font-semibold'}><Text className={'bg-gradient-to-r from-[#7D173D] to-[#0A1E5F] bg-clip-text text-transparent'}>Hi～欢迎来到吴忠！</Text>👏</View>
                <View className={'absolute z-[2] top-[60px] right-[35px] flex items-center p-[4px_12px] gap-[4px] rounded-[30px] bg-gradient-to-r from-[#ABEBF3] to-[#EB9CE2]'}>
                    <View className={'text-[13px] font-semibold bg-gradient-to-r from-[#304268] to-[#7F1074] bg-clip-text text-transparent'}>DeepSeek问吴忠吃住玩攻略</View>
                    <Image className={'size-[14px]'} src={require('../../assets/arrow-right.svg')} />
                </View>
            </View>
            <TabBar />
        </View>
        {coupon.banner &&
            <View className={'px-[15px] mb-[10px]'}>
                <Navigator url={`/pages/coupon/list`} className={'relative flex'}>
                    <Image className={'w-full'} mode={'widthFix'} src={getImageUrl(coupon.banner)} />
                </Navigator>
            </View>}
        <RecommendList />
        <SafeArea position={'bottom'} />

        {/* 入驻选择菜单 */}
        {showRegistrationMenu && (
            <View className={'fixed inset-0 z-[9999] flex items-center justify-center animate-fade-in'}>
                {/* 遮罩层 */}
                <View
                    className={'absolute inset-0 bg-black bg-opacity-50 transition-opacity duration-300'}
                    onClick={() => setShowRegistrationMenu(false)}
                />

                {/* 菜单内容 */}
                <View className={'relative bg-white rounded-[16px] mx-[40px] p-[24px] w-[280px] shadow-lg animate-scale-in'}>
                    <View className={'text-[18px] font-semibold text-center mb-[20px]'}>选择入驻类型</View>

                    <View className={'flex flex-col gap-[12px]'}>
                        <View
                            className={'flex items-center justify-center py-[12px] px-[20px] bg-[#58B4BF] text-white rounded-[8px] text-[16px] font-medium'}
                            onClick={() => {
                                setShowRegistrationMenu(false);
                                Taro.navigateTo({
                                    url: '/pages/eatery/create'
                                });
                            }}
                        >
                            商家入驻
                        </View>

                        <View
                            className={'flex items-center justify-center py-[12px] px-[20px] bg-[#58B4BF] text-white rounded-[8px] text-[16px] font-medium'}
                            onClick={() => {
                                setShowRegistrationMenu(false);
                                Taro.navigateTo({
                                    url: '/pages/spot/create'
                                });
                            }}
                        >
                            景点入驻
                        </View>

                        <View
                            className={'flex items-center justify-center py-[12px] px-[20px] bg-[#58B4BF] text-white rounded-[8px] text-[16px] font-medium'}
                            onClick={() => {
                                setShowRegistrationMenu(false);
                                Taro.navigateTo({
                                    url: '/pages/hotel/create'
                                });
                            }}
                        >
                            酒店入驻
                        </View>

                        <View
                            className={'flex items-center justify-center py-[12px] px-[20px] border border-[#E5E5E5] text-[#666] rounded-[8px] text-[16px] font-medium mt-[8px]'}
                            onClick={() => setShowRegistrationMenu(false)}
                        >
                            取消
                        </View>
                    </View>
                </View>
            </View>
        )}
    </View>;
}
