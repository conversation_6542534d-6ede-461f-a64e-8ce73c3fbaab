import { SafeArea } from '@nutui/nutui-react-taro';
import { Image, Navigator, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import Background from '../../components/background';
import NavBar from '../../components/nav-bar';
import PageTitle from '../../components/page-title';
import Request from '../../components/request';
import useRequest from '../../hooks/use-request';
import useUser from '../../hooks/use-user';
import { formatCurrency, getAvatar } from '../../utils';

const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) {
        return '早上好';
    } else if (hour < 18) {
        return '下午好';
    } else {
        return '晚上好';
    }
};

export default function Index() {
    const user = useUser();

    if (!user) {
        return null;
    }

    const { result: eatery } = useRequest<Eatery | null>({
        url: 'user/eatery',
    });

    const { result: spot } = useRequest<Spot | null>({
        url: 'user/spot',
    });

    const { result: hotel } = useRequest<Hotel | null>({
        url: 'user/hotel',
    });

    const { result: coupon } = useRequest({
        url: 'user/coupon/valid'
    });

    const scan = () => {
        Taro.scanCode({
            onlyFromCamera: true,
            scanType: ['qrCode'],
            success: (res) => {
                Taro.navigateTo({
                    url: `/pages/coupon/redeem?code=${res.result}`
                });
            },
        });
    };

    return <View className={'bg-white min-h-screen'}>
        <Background>
            <NavBar>
                <PageTitle />
            </NavBar>
            <View className={'p-3 flex gap-3 items-center'}>
                <Image className={'size-[64px] rounded-[50%]'} src={getAvatar(user.avatar)} />
                <View className={'flex flex-col gap-1'}>
                    <View className={'text-[20px] font-semibold flex items-center gap-2'}>
                        <View>{user.nickname}</View>
                        {eatery &&
                            <View className={'px-1.5 py-0.5 rounded-[32px] text-white bg-[#FF724C] text-[11px]'}>商家</View>}
                        {spot &&
                            <View className={'px-1.5 py-0.5 rounded-[32px] text-white bg-[#4CAF50] text-[11px]'}>景点</View>}
                        {hotel &&
                            <View className={'px-1.5 py-0.5 rounded-[32px] text-white bg-[#2196F3] text-[11px]'}>酒店</View>}
                    </View>
                    <View className={'text-[13px] text-[#666666]'}>{getGreeting()}，欢迎来到吴忠</View>
                </View>
            </View>
        </Background>
        <View>
            <View className={'p-[15px] flex flex-col gap-3'}>
                {coupon && <View className={'p-[1px] pt-[3px] bg-[#ACDADF]  rounded-[12px]'}>
                    <View className={'bg-white rounded-[12px] p-[16px_12px] flex items-center'}>
                        <View className={'flex-1 flex flex-col gap-[5px]'}>
                            <View className={'text-[17px] font-semibold'}>您有一张消费券待使用</View>
                            <View className={'text-[13px] text-[#999]'}>向商家出示二维码可抵扣{coupon.amount}元</View>
                        </View>
                        <Navigator url={`/pages/coupon/detail?id=${coupon.pivot.id}`} className={'p-[8px_12px] rounded-[20px] text-white text-[13px] bg-[#58B4BF] font-semibold'}>出示二维码</Navigator>
                    </View>
                </View>}
                {eatery && <View className={'p-[1px] pt-[3px] bg-[#FF724C80]  rounded-[12px]'}>
                    <View className={'bg-gradient-FF724C rounded-[12px]'}>
                        <View className={'p-3 text-white '}>
                            <View className={'text-[17px] flex items-center gap-2 font-semibold'}>
                                <Image className={'size-5'} src={require('../../assets/eatery.svg')} />
                                {eatery.name}
                                <Image
                                    className={'size-5 ms-auto'}
                                    src={require('../../assets/edit.svg')}
                                    onClick={() => {
                                        Taro.navigateTo({
                                            url: '/pages/eatery/create?from=user'
                                        });
                                    }}
                                />
                            </View>
                            {eatery.status === 1 &&
                                <View className={'mt-4 flex flex-col gap-2'}>
                                    <View className={'text-[13px]'}>已核销金额(本月)</View>
                                    <View className={'flex items-baseline'}>
                                        <Text className={'text-[18px]'}>￥</Text>
                                        <Text className={'text-[32px]'}>
                                            <Request url={'eatery/coupon/amount'}>
                                                {(data) => {
                                                    return formatCurrency(data.amount);
                                                }}
                                            </Request>
                                        </Text>
                                        <Navigator url={'/pages/eatery/coupon'} className={'flex items-center'}>
                                            <Text className={'text-[13px] ms-3'}>查看明细</Text>
                                            <Image src={require('../../assets/right-white.svg')} className={'size-[16px]'} />
                                        </Navigator>
                                    </View>
                                </View>}
                        </View>
                        {eatery.status === 0 && //审核
                            <View className={'rounded-[12px] py-6 px-4 bg-white flex flex-col justify-center items-center gap-1'}>
                                <Image className={'size-[64px]'} src={require('../../assets/review.png')} />
                                <View className={'text- [15px]'}>店铺信息审核中...</View>
                            </View>}
                        {eatery.status === 2 && //未通过
                            <View className={'rounded-[12px] py-6 px-4 bg-white flex flex-col justify-center items-center gap-1'}>
                                <Image className={'size-[64px]'} src={require('../../assets/review.png')} />
                                <View className={'text- [15px]'}>店铺信息审核未通过</View>
                            </View>}
                        {eatery.status === 1 &&
                            <View className={'rounded-[12px] py-3 px-4 bg-white flex items-center justify-around gap-1'}>
                                <Navigator url={'/pages/food/index'} className={'flex flex-col items-center gap-2'}>
                                    <Image src={require('../../assets/bowl.svg')} className={'size-[20px]'} />
                                    <View className={'text-[15px]'}>商品管理</View>
                                </Navigator>
                                {eatery.pivot.access_level === 60 && <>
                                    <View className={'w-[1px] h-[20px] bg-black opacity-10'} />
                                    <Navigator url={'/pages/member/index'} className={'flex flex-col items-center gap-2'}>
                                        <Image src={require('../../assets/peoples.svg')} className={'size-[20px]'} />
                                        <View className={'text-[15px]'}>员工管理</View>
                                    </Navigator>
                                </>}
                                <View className={'w-[1px] h-[20px] bg-black opacity-10'} />
                                <View onClick={scan} className={'flex flex-col items-center gap-2'}>
                                    <Image src={require('../../assets/scan.svg')} className={'size-[20px]'} />
                                    <View className={'text-[15px]'}>扫码核销</View>
                                </View>
                                {!!eatery.can_reserve && (
                                    <>
                                        <View className={'w-[1px] h-[20px] bg-black opacity-10'} />
                                        <Navigator url={'/pages/reserve/index'} className={'flex flex-col items-center gap-2'}>
                                            <Image src={require('../../assets/calendar.svg')} className={'size-[20px]'} />
                                            <View className={'text-[15px]'}>预定管理</View>
                                        </Navigator>
                                    </>
                                )}
                            </View>}
                    </View>
                </View>}
                {spot && <View className={'p-[1px] pt-[3px] bg-[#FF724C80]  rounded-[12px]'}>
                    <View className={'bg-gradient-FF724C rounded-[12px]'}>
                        <View className={'p-3 text-white '}>
                            <View className={'text-[17px] flex items-center gap-2 font-semibold'}>
                                <Image className={'size-5'} src={require('../../assets/eatery.svg')} />
                                {spot.name}
                                <Image
                                    className={'size-5 ms-auto'}
                                    src={require('../../assets/edit.svg')}
                                    onClick={() => {
                                        Taro.navigateTo({
                                            url: '/pages/spot/create?from=user'
                                        });
                                    }}
                                />
                            </View>
                            <View className={'mt-4 flex flex-col gap-2'}>
                                <View className={'text-[13px]'}>已核销金额(本月)</View>
                                <View className={'flex items-baseline'}>
                                    <Text className={'text-[18px]'}>￥</Text>
                                    <Text className={'text-[32px]'}>
                                        <Request url={'spot/coupon/amount'}>
                                            {(data) => {
                                                return formatCurrency(data.amount);
                                            }}
                                        </Request>
                                    </Text>
                                    <Text className={'text-[13px] ms-3'}>查看明细</Text>
                                </View>
                            </View>
                        </View>
                        <View className={'rounded-[12px] py-3 px-4 bg-white flex items-center justify-center gap-1'}>
                            <View onClick={scan} className={'flex flex-col items-center gap-2'}>
                                <Image src={require('../../assets/scan.svg')} className={'size-[20px]'} />
                                <View className={'text-[15px]'}>扫码核销</View>
                            </View>
                            {user.role === 'admin' && <View onClick={() => {
                                Taro.navigateTo({
                                    url: '/pages/eatery/staff'
                                });
                            }} className={'flex flex-col items-center gap-2'}>
                                <Image src={require('../../assets/staff.svg')} className={'size-[20px]'} />
                                <View className={'text-[15px]'}>员工管理</View>
                            </View>}
                        </View>
                    </View>
                </View>}
                {hotel && <View className={'p-[1px] pt-[3px] bg-[#FF724C80]  rounded-[12px]'}>
                    <View className={'bg-gradient-FF724C rounded-[12px]'}>
                        <View className={'p-3 text-white '}>
                            <View className={'text-[17px] flex items-center gap-2 font-semibold'}>
                                <Image className={'size-5'} src={require('../../assets/eatery.svg')} />
                                {hotel.name}
                                <Image
                                    className={'size-5 ms-auto'}
                                    src={require('../../assets/edit.svg')}
                                    onClick={() => {
                                        Taro.navigateTo({
                                            url: '/pages/hotel/create?from=user'
                                        });
                                    }}
                                />
                            </View>
                            <View className={'mt-4 flex flex-col gap-2'}>
                                <View className={'text-[13px]'}>已核销金额(本月)</View>
                                <View className={'flex items-baseline'}>
                                    <Text className={'text-[18px]'}>￥</Text>
                                    <Text className={'text-[32px]'}>
                                        <Request url={'hotel/coupon/amount'}>
                                            {(data) => {
                                                return formatCurrency(data.amount);
                                            }}
                                        </Request>
                                    </Text>
                                    <Text className={'text-[13px] ms-3'}>查看明细</Text>
                                </View>
                            </View>
                        </View>
                        <View className={'rounded-[12px] py-3 px-4 bg-white flex items-center justify-center gap-1'}>
                            <View onClick={scan} className={'flex flex-col items-center gap-2'}>
                                <Image src={require('../../assets/scan.svg')} className={'size-[20px]'} />
                                <View className={'text-[15px]'}>扫码核销</View>
                            </View>
                            {user.role === 'admin' && <View onClick={() => {
                                Taro.navigateTo({
                                    url: '/pages/eatery/staff'
                                });
                            }} className={'flex flex-col items-center gap-2'}>
                                <Image src={require('../../assets/staff.svg')} className={'size-[20px]'} />
                                <View className={'text-[15px]'}>员工管理</View>
                            </View>}
                        </View>
                    </View>
                </View>}
                <Navigator url={'/pages/user/coupon'} className={'shadow-custom rounded-[12px] p-[15px] flex items-center gap-2 text-[15px]'}>
                    <Image className={'size-5'} src={require('../../assets/ticket.svg')} />
                    消费券
                    <Image className={'size-5 ms-auto'} src={require('../../assets/right.svg')} />
                </Navigator>
                <Navigator url={'/pages/user/star'} className={'shadow-custom rounded-[12px] p-[15px] flex items-center gap-2 text-[15px]'}>
                    <Image className={'size-5'} src={require('../../assets/favorite.svg')} />
                    我的收藏
                    <Image className={'size-5 ms-auto'} src={require('../../assets/right.svg')} />
                </Navigator>
            </View>
            <View className={'px-[15px]'}>
                <View onClick={() => {
                    Taro.removeStorageSync('user');
                    Taro.redirectTo({
                        url: '/pages/index/index'
                    });
                }} className={'mb-[15px] rounded-[10px] bg-[#F6F6F6] text-[#F4495C] text-[16px] p-[13px] flex items-center justify-center'}>退出登录</View>
                <SafeArea position={'bottom'} />
            </View>
        </View>
    </View>;
}
